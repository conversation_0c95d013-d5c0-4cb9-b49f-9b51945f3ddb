import React, { useEffect } from "react";
import Web<PERSON>avbar from "../components/web_navbar";
import Chatbot from "react-chatbot-kit";
import "react-chatbot-kit/build/main.css";
import config from "../configs/chatbotConfig";
import AIMessageParser from "../chatbot/AIMessageParser";
import AIActionProvider from "../chatbot/AIActionProvider.js";
import { createClientMessage } from "react-chatbot-kit";

const StrutsAI = () => {
  useEffect(() => {
    window.scrollTo(0, 0);
    createClientMessage("Hello there.");
  }, []);

  return (
    <>
      <WebNavbar />
      <section className="section">
        <div className="container">
          <section className="section">
            <h1 className="title">Struts AI</h1>
            <h2 className="subtitle">
              Unleash the Power of AI to Revolutionize Your Business.
            </h2>

            <div className="columns">
              <div className="column is-three-fifths">
                <br />
                <h3 className="title is-4">
                  Elevate Your Business with Automated Chat Solutions!{" "}
                </h3>
                <b>Unlock the Power of Chatbots:</b> Train Your Personal
                Assistant to Engage Customers, Gather Information, and Follow Up
                Seamlessly.
                <br /> <br />
                <b>Say Hello to Effortless Customer Support:</b> Harness the
                Potential of AI-Powered Chatbots to Handle Queries and Boost
                Your Sales!
                <br /> <br />
                <b>Customize Your Conversations:</b> Craft Tailored Interactions
                and Capture Customer Details for Future Success. Don't Miss Out
                on Opportunities - Automate and Prosper!
                <br />
                <br />
                <div>
                  <div className="buttons">
                    <a
                      href="https://ai.strutstechnology.com"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="button is-primary"
                    >
                      <strong>Get Started</strong>
                    </a>
                  </div>
                </div>
              </div>
              <div className="column is-1">&nbsp;</div>
              <div className="column">
                <div
                  style={{
                    border: "2px solid #00d1b2",
                    borderRadius: "7.5px",
                    width: "max-content",
                    marginTop: "-65px",
                  }}
                >
                  <Chatbot
                    config={config}
                    messageParser={AIMessageParser}
                    actionProvider={AIActionProvider}
                  />
                </div>
              </div>
            </div>
          </section>
        </div>
      </section>
    </>
  );
};

export default StrutsAI;
