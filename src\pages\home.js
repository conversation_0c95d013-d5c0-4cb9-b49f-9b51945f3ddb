import { useEffect, useRef } from "react";
import { Link, useNavigate } from "react-router-dom";
import HeroHeader from "../components/hero-header";
import SoftwareCard from "../components/SoftwareCard";
import WebFooter from "../components/web-footer";

const Home = () => {
  const navigate = useNavigate();

  let heroHeader = useRef();

  heroHeader.current = HeroHeader(
    "Struts Technology",
    "We offer custom made software tailored to your specification."
  );

  useEffect(() => {
    let accessToken = localStorage.getItem("esd_access_token");
    if (accessToken) {
      navigate("/dashboard");
    }

    window.scrollTo(0, 0);
  }, [navigate]);

  return (
    <>
      {/* <WebNavbar /> */}
      {heroHeader.current}
      <section className="section">
        <div className="container">
          <h1 className="title">ESD Ws Portal</h1>
          <p className="subtitle">
            Electronic Signature Device signing, reporting and analytics tool.
          </p>

          <br />

          <div class="columns" style={{ marginTop: "5px" }}>
            <div class="column">
              <a
                href="https://agriprofit.strutstechnology.com/"
                target="_blank"
                rel="noreferrer"
              >
                <SoftwareCard
                  name="Custom Made Software"
                  handle="TechStruts"
                  hashTags="#TechStruts"
                  description="We offer custom made software tailored to your specification."
                  imgSrc="/assets/agriprofit.png"
                />
              </a>
            </div>
            <div class="column">
              <Link className="" to="/struts-ai">
                <SoftwareCard
                  name="Struts AI"
                  handle="StrutsAI"
                  hashTags="#TechStruts #StrutsAI"
                  description="Unleash the Power of AI to Revolutionize Your Business."
                  imgSrc="/assets/struts-ai-home.png"
                />
              </Link>
            </div>
            <div class="column">
              <Link className="" to="/kodi-africa">
                <SoftwareCard
                  name="Kodi Africa"
                  handle="Kodi_Africa"
                  hashTags="#TechStruts #kodiAfrica"
                  description="We automate invoice and receipt documents processing for tax compliance."
                  imgSrc="/assets/kodi-africa-dashboard.png"
                />
              </Link>
            </div>
          </div>
        </div>
      </section>
      <WebFooter />
    </>
  );
};

export default Home;
