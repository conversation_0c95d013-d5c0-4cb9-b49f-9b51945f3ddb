import { Routes, Route } from "react-router-dom";

import Home from "./pages/home";
import Login from "./pages/login";
import SignUp from "./pages/sign_up";

// Admin dashboard routes
import Analytics from "./pages/admin/analytics";
import Configurations from "./pages/admin/configurations";
import Contact from "./pages/contact";
import Dashboard from "./pages/admin/dashboard";
import Invoices from "./pages/admin/invoices";
import InvoicesEmailFail from "./pages/admin/invoices/email-fail";
import Invoice from "./pages/admin/invoices/id";
import KodiAfrica from "./pages/kodi-africa";
import Profile from "./pages/admin/profile";
import StrutsAI from "./pages/struts-ai";
import SupportEngine from "./SupportEngine";
import VFDConfiguration from "./pages/admin/configurations/vfd_configuration";
import XeroCallback from "./pages/xero-callback";
import ZReports from "./pages/admin/zreports";
import ZReport from "./pages/admin/zreports/id";

import "./App.css";

const App = () => {
  return (
    <>
      <Routes>
        <Route path="/" element={<Home />} exact />
        <Route path="/home" element={<Home />} />
        <Route path="/login" element={<Login />} />
        <Route path="/signup" element={<SignUp />} />

        <Route path="/contact" element={<Contact />} />
        <Route path="/dashboard" element={<Dashboard />} />
        <Route path="/invoices" element={<Invoices />} />
        <Route path="/invoices-email-fail" element={<InvoicesEmailFail />} />
        <Route path="/invoices/:id" element={<Invoice />} />
        <Route path="/kodi-africa" element={<KodiAfrica />} />
        <Route path="/analytics" element={<Analytics />} />
        <Route path="/profile" element={<Profile />} />
        <Route path="/struts-ai" element={<StrutsAI />} />
        <Route path="/configurations" element={<Configurations />} />
        <Route path="/vfd-configuration" element={<VFDConfiguration />} />
        <Route path="/xero-callback" element={<XeroCallback />} />
        <Route path="/zreports" element={<ZReports />} />
        <Route path="/zreports/:id" element={<ZReport />} />
      </Routes>

      <SupportEngine />
    </>
  );
};

export default App;
