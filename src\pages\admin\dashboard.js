import { useEffect, useState } from "react";
import AdminNavbar from "../../components/admin_navbar";
import AdminSideMenu from "../../components/admin_side_menu";
import BarChart from "../../components/bar_chart";
import axios from "axios";
import { Link, useNavigate } from "react-router-dom";

const Dashboard = () => {
  const navigate = useNavigate();

  const [totalInvoicesCount, setTotalInvoicesCount] = useState(0);
  const [signedInvoicesCount, setSignedInvoicesCount] = useState(0);
  const [failedInvoicesCount, setFailedInvoicesCount] = useState(0);
  const [processingInvoicesCount, setProcessingCount] = useState(0);
  const [chartData, setChartData] = useState();

  const getDashboardAnalytics = async () => {
    var apiBaseURL = process.env.REACT_APP_API_BASE_URL;
    var accessToken = localStorage.getItem("esd_access_token");

    try {
      const res = await axios({
        url: apiBaseURL + "/analytics/dashboard",
        headers: {
          "x-esdws-token": accessToken,
          "x-esdws-application": "user",
        },
        method: "GET",
      });

      let dashboardAnalytics = JSON.parse(JSON.stringify(res.data));
      setTotalInvoicesCount(dashboardAnalytics.total_invoices_count);
      setFailedInvoicesCount(dashboardAnalytics.failed_invoices_count);
      setSignedInvoicesCount(dashboardAnalytics.signed_invoices_count);
      setProcessingCount(dashboardAnalytics.processing_invoices_count);

      let chartSummariesData = dashboardAnalytics.chart_summary;

      setChartData({
        labels: chartSummariesData.map((data) => data.month),
        datasets: [
          {
            label: "Invoices Signed",
            data: chartSummariesData.map((data) => data.total_invoices),
          },
        ],
      });
    } catch (err) {
      console.error("getDashboardAnalytics err = " + err);
    }
  };

  useEffect(() => {
    let accessToken = localStorage.getItem("esd_access_token");
    if (!accessToken) {
      navigate("/");
    }

    window.scrollTo(0, 0);

    getDashboardAnalytics();
  }, [navigate]);

  return (
    <>
      <AdminNavbar />

      <section className="section">
        <div className="container">
          <h1 className="title">Dashboard</h1>

          <div className="columns">
            <div className="column is-one-quarter">
              <AdminSideMenu />
            </div>
            <div className="column is-three-quarters">
              <p>
                <b>Dashboard</b>
              </p>

              <br />

              <div className="tile is-ancestor">
                <div className="tile is-horizontal is-parent">
                  <Link to="/invoices">
                    <article className="tile is-child box">
                      <p className="title">{totalInvoicesCount}</p>
                      <p className="subtitle">Total Processed</p>
                    </article>
                  </Link>
                </div>
                <div className="tile is-parent">
                  <Link to="/invoices">
                    <article className="tile is-child box">
                      <p className="title has-text-info">
                        {processingInvoicesCount}
                      </p>
                      <p className="subtitle">Currently Processing</p>
                    </article>
                  </Link>
                </div>
                <div className="tile is-parent">
                  <Link to="/invoices">
                    <article className="tile is-child box">
                      <p className="title has-text-primary">
                        {signedInvoicesCount}
                      </p>
                      <p className="subtitle">Signed Successfully</p>
                    </article>
                  </Link>
                </div>
                <div className="tile is-parent">
                  <Link to="/invoices">
                    <article className="tile is-child box">
                      <p className="title has-text-warning">
                        {failedInvoicesCount}
                      </p>
                      <p className="subtitle">Failed to Process</p>
                    </article>
                  </Link>
                </div>
              </div>

              <br />
              <br />

              <div className="tile is-ancestor">
                {/* <PieChart chartData={chartData} /> */}

                {/* <Line
                  data={barChartConfig.data}
                  options={barChartConfig.options}
                />
                <Pie
                  data={barChartConfig.data}
                  options={barChartConfig.options}
                /> */}

                {/* {chartRender} */}

                {/* <Bar data={chartData} /> */}

                {chartData && (
                  <BarChart
                    labels={chartData.labels}
                    datasets={chartData.datasets}
                  />
                )}
              </div>

              <br />
              <br />
              <br />

              {/* {{.bar}} */}
            </div>
          </div>
        </div>
      </section>
    </>
  );
};

export default Dashboard;
