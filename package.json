{"name": "bulma-react-admin", "version": "0.1.0", "private": true, "proxy": "http://localhost:9010", "dependencies": {"@fortawesome/fontawesome": "^1.1.8", "@fortawesome/fontawesome-svg-core": "^6.4.2", "@fortawesome/free-brands-svg-icons": "^6.4.2", "@fortawesome/free-regular-svg-icons": "^6.4.2", "@fortawesome/free-solid-svg-icons": "^6.4.2", "@fortawesome/react-fontawesome": "^0.2.0", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.1.3", "bulma": "^0.9.4", "bulma-calendar": "^6.1.19", "bulma-pagination-react": "^0.1.1", "chart.js": "^4.0.1", "dotenv": "^16.0.3", "env-cmd": "^10.1.0", "faker": "^6.6.6", "react": "^18.2.0", "react-chartjs-2": "^5.0.1", "react-chatbot-kit": "^2.1.2", "react-datepicker": "^4.23.0", "react-dom": "^18.2.0", "react-paginate": "^8.1.4", "react-pdf": "^6.1.1", "react-router-dom": "^6.4.3", "react-scripts": "5.0.1", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}