pipeline { 
    agent any
    
    tools {nodejs "NodeJS"}
    
    stages {  
        stage('Checkout') {
            steps{
                git branch: 'main', credentialsId: '0a5e679f-c7c6-4b69-aea7-9b42c28915c0', url: 'https://gitlab.com/karuga.gideon/struts-vfd-ai-web'                                
            }
        }

        // TODO: Return this later
        // stage('Test'){
        //     steps {
        //         sh 'npm run test' 
        //     }
        // }
        
        stage('Build'){
            steps {
                sh 'npm install'
                sh 'npm run build'
            }
        }
        
        stage('Deploy') {
            steps {
                // Stage website update
                sh "sudo rm -rf /var/www/stage.strutstechnology.co.ke"
                sh "sudo mkdir /var/www/stage.strutstechnology.co.ke"
                sh "sudo cp -r ./build/* /var/www/stage.strutstechnology.co.ke"

                // co.ke website update
                sh "sudo rm -rf /var/www/strutstechnology.co.ke"
                sh "sudo mkdir /var/www/strutstechnology.co.ke"
                sh "sudo cp -r ./build/* /var/www/strutstechnology.co.ke"

                // .com website update
                sh "sudo rm -rf /var/www/strutstechnology.com"
                sh "sudo mkdir /var/www/strutstechnology.com"
                sh "sudo cp -r ./build/* /var/www/strutstechnology.com"
            }
        }
    }
}